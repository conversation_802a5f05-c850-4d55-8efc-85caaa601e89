'use client';

import React from 'react';
import { ChatSidebar } from './ChatSidebar';
import { ChatMessageArea } from './ChatMessageArea';
import { ChatMessageInput } from './ChatMessageInput';

export const ChatContainer = () => {
  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat main container */}
      <div className="flex flex-1 overflow-hidden">
        <ChatSidebar />
        <div className="flex-1 flex flex-col">
          <ChatMessageArea />
          <ChatMessageInput />
        </div>
      </div>
    </div>
  );
};
