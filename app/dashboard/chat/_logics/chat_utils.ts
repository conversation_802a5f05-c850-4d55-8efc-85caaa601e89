import { ChatWebSocketService } from "./websocket_service";
import { getCurrentUser } from "./websocket_service";

/**
 * Utility functions for common chat operations
 * This file contains helper functions that don't require API calls
 */

/**
 * Connect to a specific chat group using WebSocket
 * This function handles the WebSocket connection logic
 *
 * @param chatGroupId - The chat group ID to connect to
 * @returns Promise<boolean> - True if connection was initiated successfully, false otherwise
 */
export async function connectToSpecificChatGroup(
  chatGroupId: string
): Promise<boolean> {
  try {
    console.log(`Attempting to connect to chat group: ${chatGroupId}`);

    // Validate input
    if (!chatGroupId || typeof chatGroupId !== "string") {
      console.error("Invalid chat group ID provided");
      return false;
    }

    // Check if user is authenticated
    const user = getCurrentUser();
    if (!user) {
      console.error("No authenticated user found");
      return false;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error("No valid user ID found");
      return false;
    }

    console.log(`User ${userId} connecting to chat group ${chatGroupId}`);

    // Get WebSocket service instance
    const webSocketService = ChatWebSocketService.getInstance();

    // Connect via WebSocket
    webSocketService.connectToSpecificChatGroup(chatGroupId);

    console.log(
      `Successfully initiated connection to chat group ${chatGroupId}`
    );
    return true;
  } catch (error) {
    console.error(
      `Error connecting to specific chat group ${chatGroupId}:`,
      error
    );
    return false;
  }
}

/**
 * Get the WebSocket URL for a specific chat group
 *
 * @param chatGroupId - The chat group ID
 * @returns string | null - The WebSocket URL or null if user is not authenticated
 */
export function getWebSocketUrlForChatGroup(
  chatGroupId: string
): string | null {
  try {
    const user = getCurrentUser();
    if (!user) {
      console.error("No authenticated user found");
      return null;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error("No valid user ID found");
      return null;
    }

    const wsBaseUrl = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL;
    if (!wsBaseUrl) {
      console.error("WebSocket base URL not configured");
      return null;
    }

    return `${wsBaseUrl}/${chatGroupId}?user_id=${userId}`;
  } catch (error) {
    console.error("Error generating WebSocket URL:", error);
    return null;
  }
}

/**
 * Check if a user can connect to a specific chat group
 * This function verifies authentication
 *
 * @param chatGroupId - The chat group ID
 * @returns boolean - True if user can connect, false otherwise
 */
export function canConnectToChatGroup(chatGroupId: string): boolean {
  try {
    // Check authentication
    const user = getCurrentUser();
    if (!user) {
      console.error("No authenticated user found");
      return false;
    }

    const userId = user.student_id || user.teacher_id || user.id;
    if (!userId) {
      console.error("No valid user ID found");
      return false;
    }

    // Check if chat group ID is valid
    if (!isValidChatGroupId(chatGroupId)) {
      console.error("Invalid chat group ID format");
      return false;
    }

    return true;
  } catch (error) {
    console.error(
      `Error checking if user can connect to chat group ${chatGroupId}:`,
      error
    );
    return false;
  }
}

/**
 * Validate a chat group ID format
 *
 * @param chatGroupId - The chat group ID to validate
 * @returns boolean - True if the format appears valid, false otherwise
 */
export function isValidChatGroupId(chatGroupId: string): boolean {
  if (!chatGroupId || typeof chatGroupId !== "string") {
    return false;
  }

  // Check if it's a UUID format (basic validation)
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(chatGroupId);
}

/**
 * Get the current WebSocket base URL for chat
 */
export function getChatWebSocketUrl(): string {
  return process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL || "";
}

/**
 * Format a timestamp for display in chat
 *
 * @param timestamp - The timestamp to format
 * @returns string - Formatted timestamp
 */
export function formatChatTimestamp(timestamp: Date): string {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) {
    return "Just now";
  } else if (minutes < 60) {
    return `${minutes}m ago`;
  } else if (hours < 24) {
    return `${hours}h ago`;
  } else if (days < 7) {
    return `${days}d ago`;
  } else {
    return timestamp.toLocaleDateString();
  }
}

/**
 * Generate a unique message ID
 *
 * @returns string - Unique message ID
 */
export function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Sanitize message content for display
 *
 * @param content - The message content to sanitize
 * @returns string - Sanitized content
 */
export function sanitizeMessageContent(content: string): string {
  if (!content || typeof content !== "string") {
    return "";
  }

  // Basic HTML escaping
  return content
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#x27;")
    .trim();
}

/**
 * Check if a message is from the current user
 *
 * @param senderId - The sender ID of the message
 * @returns boolean - True if message is from current user
 */
export function isMessageFromCurrentUser(senderId: string): boolean {
  const user = getCurrentUser();
  if (!user) return false;

  const userId = user.student_id || user.teacher_id || user.id;
  return userId === senderId;
}

/**
 * Get user display name
 *
 * @param user - The user object
 * @returns string - Display name
 */
export function getUserDisplayName(user: any): string {
  if (!user) return "Unknown User";

  if (user.name) return user.name;
  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }
  if (user.first_name) return user.first_name;
  if (user.email) return user.email;

  return "Unknown User";
}

/**
 * Get user avatar URL
 *
 * @param user - The user object
 * @returns string - Avatar URL
 */
export function getUserAvatarUrl(user: any): string {
  if (user?.avatar) return user.avatar;
  return "/logo.png"; // Default avatar
}

/**
 * Debounce function for search operations
 *
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Function - Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Check if WebSocket is supported by the browser
 *
 * @returns boolean - True if WebSocket is supported
 */
export function isWebSocketSupported(): boolean {
  return typeof WebSocket !== "undefined";
}

/**
 * Get connection status text
 *
 * @param isConnected - Connection status
 * @returns string - Status text
 */
export function getConnectionStatusText(isConnected: boolean): string {
  return isConnected ? "Connected" : "Disconnected";
}

/**
 * Format room name for display
 *
 * @param roomName - The room name
 * @param maxLength - Maximum length for display
 * @returns string - Formatted room name
 */
export function formatRoomName(roomName: string, maxLength: number = 30): string {
  if (!roomName) return "Unknown Room";
  
  if (roomName.length <= maxLength) return roomName;
  
  return `${roomName.substring(0, maxLength - 3)}...`;
}
