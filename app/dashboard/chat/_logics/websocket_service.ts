import { ChatMessage, WebSocketMessage, User } from "./types";

// Get current user from cookies/storage (simplified version for dashboard)
export const getCurrentUser = (): User | null => {
  try {
    // This is a simplified version - in production you'd get this from your auth system
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');
      if (userStr) {
        return JSON.parse(userStr);
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Format WebSocket message to match our ChatMessage format
export const formatWebSocketMessage = (
  wsMessage: WebSocketMessage
): ChatMessage => {
  return {
    id: wsMessage.id || wsMessage._id || `${Date.now()}-${Math.random()}`,
    senderId: wsMessage.sender_id || wsMessage.sender?.id || "unknown",
    senderName:
      wsMessage.sender_name || wsMessage.sender?.name || "Unknown User",
    senderAvatar:
      wsMessage.sender_avatar || wsMessage.sender?.avatar || "/logo.png",
    content: wsMessage.content || "",
    timestamp: new Date(
      wsMessage.created_at || wsMessage.timestamp || Date.now()
    ),
  };
};

/**
 * Singleton WebSocket Service for chat
 * Supports three WebSocket endpoints:
 * 1. /ws/chat/{room_id} - Connect to a chat room
 * 2. /ws/chat/direct/{recipient_id} - Send and receive direct chat messages
 * 3. /ws/notifications/{user_id} - Receive real-time notifications
 */
export class ChatWebSocketService {
  private static instance: ChatWebSocketService;
  private socket: WebSocket | null = null;
  private notificationsSocket: WebSocket | null = null;
  private connectionStateChangeCallbacks: ((connected: boolean) => void)[] = [];
  private messageCallbacks: ((message: WebSocketMessage) => void)[] = [];
  private currentRoom: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private heartbeatIntervalMs = 30000; // 30 seconds

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): ChatWebSocketService {
    if (!ChatWebSocketService.instance) {
      ChatWebSocketService.instance = new ChatWebSocketService();
    }
    return ChatWebSocketService.instance;
  }

  /**
   * Connect to a specific chat room
   * @param roomId The room ID to connect to
   */
  public connectToRoom(roomId: string): void {
    this.disconnect();
    this.currentRoom = roomId;

    console.log(`Connecting to chat room: ${roomId}`);

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("Cannot connect to WebSocket: No user found");
        this.notifyConnectionStateChange(false);
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id;
      if (!userId) {
        console.error("Cannot connect to WebSocket: No user ID found");
        this.notifyConnectionStateChange(false);
        return;
      }

      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;
      const wsUrl = `${wsBaseUrl}/${roomId}?user_id=${userId}`;

      console.log(`Connecting to WebSocket URL: ${wsUrl}`);

      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
      this.startHeartbeat();
    } catch (error) {
      console.error("Error connecting to WebSocket:", error);
      this.notifyConnectionStateChange(false);
    }
  }

  /**
   * Connect to direct chat with another user
   * @param recipientId The ID of the recipient to chat with
   */
  public connectToDirectChat(recipientId: string): void {
    this.disconnect();
    this.currentRoom = `direct-${recipientId}`;

    console.log(`Connecting to direct chat with recipient: ${recipientId}`);

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("Cannot connect to direct chat: No user found");
        this.notifyConnectionStateChange(false);
        return;
      }

      const userId = user.student_id || user.teacher_id || user.id;
      if (!userId) {
        console.error("Cannot connect to direct chat: No user ID found");
        this.notifyConnectionStateChange(false);
        return;
      }

      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_CHAT_BASE_URL!;
      const wsUrl = `${wsBaseUrl}/direct/${recipientId}?user_id=${userId}`;

      console.log(`Connecting to direct chat WebSocket URL: ${wsUrl}`);

      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
      this.startHeartbeat();
    } catch (error) {
      console.error("Error connecting to direct chat:", error);
      this.notifyConnectionStateChange(false);
    }
  }

  /**
   * Connect to a specific chat group by ID
   * @param chatGroupId The chat group ID to connect to
   */
  public connectToSpecificChatGroup(chatGroupId: string): void {
    console.log(`Connecting to specific chat group: ${chatGroupId}`);
    this.connectToRoom(chatGroupId);
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      console.log("WebSocket connected successfully");
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.notifyConnectionStateChange(true);
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log("Received WebSocket message:", message);
        this.notifyMessageReceived(message);
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    this.socket.onclose = (event) => {
      console.log("WebSocket connection closed:", event.code, event.reason);
      this.notifyConnectionStateChange(false);
      this.stopHeartbeat();

      // Attempt to reconnect if it wasn't a manual disconnect
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      this.notifyConnectionStateChange(false);
    };
  }

  /**
   * Schedule a reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

    setTimeout(() => {
      if (this.currentRoom && this.reconnectAttempts <= this.maxReconnectAttempts) {
        console.log(`Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        if (this.currentRoom.startsWith('direct-')) {
          const recipientId = this.currentRoom.replace('direct-', '');
          this.connectToDirectChat(recipientId);
        } else {
          this.connectToRoom(this.currentRoom);
        }
      }
    }, this.reconnectDelay);

    // Exponential backoff with max delay of 30 seconds
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify({ type: 'ping' }));
      }
    }, this.heartbeatIntervalMs);
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Send a message to the current chat room
   * @param content The message content to send
   */
  public sendMessage(content: string): void {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error("Cannot send message: WebSocket not connected");
      return;
    }

    try {
      const user = getCurrentUser();
      if (!user) {
        console.error("Cannot send message: No user found");
        return;
      }

      const message = {
        type: "message",
        content,
        timestamp: new Date().toISOString(),
        sender: {
          id: user.student_id || user.teacher_id || user.id || "",
          name: user.name || "Unknown User",
          avatar: user.avatar || "/logo.png",
        },
      };

      this.socket.send(JSON.stringify(message));
    } catch (error) {
      console.error("Error sending message:", error);
    }
  }

  /**
   * Disconnect from the current WebSocket connection
   */
  public disconnect(): void {
    this.stopHeartbeat();
    
    if (this.socket) {
      this.socket.close(1000, "Manual disconnect");
      this.socket = null;
    }
    
    if (this.notificationsSocket) {
      this.notificationsSocket.close(1000, "Manual disconnect");
      this.notificationsSocket = null;
    }
    
    this.currentRoom = null;
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;
  }

  /**
   * Check if WebSocket is currently connected
   */
  public isConnected(): boolean {
    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;
  }

  /**
   * Get the current room ID
   */
  public getCurrentRoom(): string | null {
    return this.currentRoom;
  }

  /**
   * Register a callback for connection state changes
   */
  public onConnectionStateChange(callback: (connected: boolean) => void): void {
    this.connectionStateChangeCallbacks.push(callback);
  }

  /**
   * Register a callback for received messages
   */
  public onMessageReceived(callback: (message: WebSocketMessage) => void): void {
    this.messageCallbacks.push(callback);
  }

  /**
   * Remove a connection state change callback
   */
  public removeConnectionStateChangeCallback(callback: (connected: boolean) => void): void {
    const index = this.connectionStateChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.connectionStateChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * Remove a message received callback
   */
  public removeMessageCallback(callback: (message: WebSocketMessage) => void): void {
    const index = this.messageCallbacks.indexOf(callback);
    if (index > -1) {
      this.messageCallbacks.splice(index, 1);
    }
  }

  /**
   * Notify all registered callbacks about connection state changes
   */
  private notifyConnectionStateChange(connected: boolean): void {
    this.connectionStateChangeCallbacks.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error("Error in connection state change callback:", error);
      }
    });
  }

  /**
   * Notify all registered callbacks about received messages
   */
  private notifyMessageReceived(message: WebSocketMessage): void {
    this.messageCallbacks.forEach(callback => {
      try {
        callback(message);
      } catch (error) {
        console.error("Error in message received callback:", error);
      }
    });
  }
}
