// Chat participant interface
export interface ChatParticipant {
  id: string;
  name: string;
  avatar: string;
}

// Chat room/group interface
export interface ChatRoom {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: Date;
  isGroup: boolean;
  avatar: string;
  participants: ChatParticipant[];
}

// Chat message interface
export interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  content: string;
  timestamp: Date;
}

// Chat state interface for managing active room and messages
export interface ChatState {
  activeRoomId: string | null;
  rooms: ChatRoom[];
  messages: Record<string, ChatMessage[]>;
  isLoading: boolean;
  error: string | null;
  courseGroupsLoaded: boolean;
}

// Chat action types for reducer
export type ChatAction =
  | { type: "SET_ACTIVE_ROOM"; payload: string }
  | { type: "ADD_MESSAGE"; payload: { roomId: string; message: ChatMessage } }
  | { type: "SET_ROOMS"; payload: ChatRoom[] }
  | { type: "ADD_ROOM"; payload: ChatRoom }
  | { type: "UPDATE_ROOM"; payload: { roomId: string; updates: Partial<ChatRoom> } }
  | { type: "SET_MESSAGES"; payload: { roomId: string; messages: ChatMessage[] } }
  | { type: "SEARCH_CHATS"; payload: string }
  | { type: "SEARCH_MESSAGES"; payload: { roomId: string; query: string } }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "SET_COURSE_GROUPS_LOADED"; payload: boolean };

// Chat group creation request interface
export interface ChatGroupCreateRequest {
  name: string;
  description: string;
  type: "student" | "teacher" | "course";
  course_id?: string;
  is_private: boolean;
  initial_members: string[];
}

// Chat group response interface
export interface ChatGroupResponse {
  id: string;
  name: string;
  description: string;
  type: "student" | "teacher" | "course";
  course_id?: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  members?: ChatGroupMember[];
}

// Chat group member interface
export interface ChatGroupMember {
  id: string;
  user_id: string;
  chat_group_id: string;
  is_admin: boolean;
  joined_at: string;
  user?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

// Chat group add member request interface
export interface ChatGroupAddMemberRequest {
  user_id: string;
  is_student: boolean;
  is_admin: boolean;
}

// User interface for authentication
export interface User {
  id?: string;
  student_id?: string;
  teacher_id?: string;
  name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  avatar?: string;
}

// Course interface for course-related functionality
export interface APICourse {
  id: string;
  title: string;
  description?: string;
  instructor?: string;
  created_at?: string;
  updated_at?: string;
}

// WebSocket message interface to handle different message formats from server
export interface WebSocketMessage {
  id?: string;
  _id?: string; // Alternative ID field from some APIs
  content?: string;
  timestamp?: string;
  created_at?: string; // Alternative timestamp field
  sender?: {
    id?: string;
    name?: string;
    avatar?: string;
  };
  sender_id?: string; // Alternative sender field format
  sender_name?: string;
  sender_avatar?: string;
  type?: string; // Message type (text, system, etc.)
  attachments?: any[];
}
