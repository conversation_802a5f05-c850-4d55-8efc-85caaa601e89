"use client";

import {
  createContext,
  useContext,
  useReducer,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { ChatMessage, ChatRoom, ChatState } from "./types";
import {
  ChatWebSocketService,
  formatWebSocketMessage,
} from "./websocket_service";

// Chat context interface for global state management
export interface ChatContextType {
  state: ChatState;
  webSocketConnected: boolean;
  setActiveRoom: (roomId: string) => void;
  sendMessage: (content: string) => void;
  searchChats: (query: string) => void;
  searchMessages: (roomId: string, query: string) => void;
  connectToSpecificChatGroup: (chatGroupId: string) => void;
}

// Initial state
const initialState: ChatState = {
  activeRoomId: null,
  rooms: [],
  messages: {},
  isLoading: false,
  error: null,
  courseGroupsLoaded: false,
};

// Chat reducer for state management
const chatReducer = (state: ChatState, action: any): ChatState => {
  switch (action.type) {
    case "SET_ACTIVE_ROOM":
      return {
        ...state,
        activeRoomId: action.payload,
      };
    case "ADD_MESSAGE":
      const { roomId, message } = action.payload;
      return {
        ...state,
        messages: {
          ...state.messages,
          [roomId]: [...(state.messages[roomId] || []), message],
        },
      };
    case "SET_ROOMS":
      return {
        ...state,
        rooms: action.payload,
      };
    case "ADD_ROOM":
      const existingRoomIndex = state.rooms.findIndex(
        (room) => room.id === action.payload.id
      );
      if (existingRoomIndex >= 0) {
        // Update existing room
        const updatedRooms = [...state.rooms];
        updatedRooms[existingRoomIndex] = action.payload;
        return {
          ...state,
          rooms: updatedRooms,
        };
      } else {
        // Add new room
        return {
          ...state,
          rooms: [...state.rooms, action.payload],
        };
      }
    case "UPDATE_ROOM":
      return {
        ...state,
        rooms: state.rooms.map((room) =>
          room.id === action.payload.roomId
            ? { ...room, ...action.payload.updates }
            : room
        ),
      };
    case "SET_MESSAGES":
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.roomId]: action.payload.messages,
        },
      };
    case "SEARCH_CHATS":
      const query = action.payload.toLowerCase();
      if (!query) {
        // If no query, show all rooms
        return state;
      }
      const filteredRooms = state.rooms.filter((room) =>
        room.name.toLowerCase().includes(query)
      );

      return { ...state, rooms: filteredRooms };
    case "SEARCH_MESSAGES":
      // Implement message search in future enhancement
      return state;
    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };
    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
      };
    default:
      return state;
  }
};

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Context provider
export const ChatProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const [webSocketConnected, setWebSocketConnected] = useState(false);

  // Services
  const webSocketService = ChatWebSocketService.getInstance();

  // WebSocket connection state handler
  useEffect(() => {
    const handleConnectionStateChange = (connected: boolean) => {
      console.log("WebSocket connection state changed:", connected);
      setWebSocketConnected(connected);
    };

    const handleMessageReceived = (wsMessage: any) => {
      console.log("Received WebSocket message in context:", wsMessage);
      
      try {
        const formattedMessage = formatWebSocketMessage(wsMessage);
        const currentRoom = webSocketService.getCurrentRoom();
        
        if (currentRoom) {
          dispatch({
            type: "ADD_MESSAGE",
            payload: {
              roomId: currentRoom,
              message: formattedMessage,
            },
          });

          // Update room's last message
          dispatch({
            type: "UPDATE_ROOM",
            payload: {
              roomId: currentRoom,
              updates: {
                lastMessage: formattedMessage.content,
                timestamp: formattedMessage.timestamp,
              },
            },
          });
        }
      } catch (error) {
        console.error("Error processing received message:", error);
      }
    };

    // Register WebSocket callbacks
    webSocketService.onConnectionStateChange(handleConnectionStateChange);
    webSocketService.onMessageReceived(handleMessageReceived);

    // Cleanup function
    return () => {
      webSocketService.removeConnectionStateChangeCallback(handleConnectionStateChange);
      webSocketService.removeMessageCallback(handleMessageReceived);
    };
  }, []);

  // Set active room and connect to WebSocket
  const setActiveRoom = (roomId: string) => {
    console.log(`Setting active room: ${roomId}`);
    dispatch({ type: "SET_ACTIVE_ROOM", payload: roomId });
    
    // Connect to the room via WebSocket
    webSocketService.connectToRoom(roomId);
  };

  // Send message
  const sendMessage = (content: string) => {
    if (!content.trim()) return;
    
    console.log(`Sending message: ${content}`);
    webSocketService.sendMessage(content);
  };

  // Search chats
  const searchChats = (query: string) => {
    dispatch({ type: "SEARCH_CHATS", payload: query });
  };

  // Search messages (placeholder for future implementation)
  const searchMessages = (roomId: string, query: string) => {
    dispatch({ type: "SEARCH_MESSAGES", payload: { roomId, query } });
  };

  // Connect to specific chat group
  const connectToSpecificChatGroup = (chatGroupId: string) => {
    console.log(`Connecting to specific chat group: ${chatGroupId}`);
    webSocketService.connectToSpecificChatGroup(chatGroupId);
  };

  // Initialize with some demo data for dashboard
  useEffect(() => {
    const initializeDemoData = () => {
      const demoRooms: ChatRoom[] = [
        {
          id: "demo-room-1",
          name: "General Discussion",
          lastMessage: "Welcome to the dashboard chat!",
          timestamp: new Date(),
          isGroup: true,
          avatar: "/logo.png",
          participants: [
            { id: "user1", name: "John Doe", avatar: "/logo.png" },
            { id: "user2", name: "Jane Smith", avatar: "/logo.png" },
          ],
        },
        {
          id: "demo-room-2",
          name: "Project Updates",
          lastMessage: "Latest updates on the project",
          timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          isGroup: true,
          avatar: "/logo.png",
          participants: [
            { id: "user1", name: "John Doe", avatar: "/logo.png" },
            { id: "user3", name: "Bob Johnson", avatar: "/logo.png" },
          ],
        },
      ];

      dispatch({ type: "SET_ROOMS", payload: demoRooms });
    };

    // Initialize demo data after a short delay
    const timer = setTimeout(initializeDemoData, 1000);
    return () => clearTimeout(timer);
  }, []);

  const value: ChatContextType = {
    state,
    webSocketConnected,
    setActiveRoom,
    sendMessage,
    searchChats,
    searchMessages,
    connectToSpecificChatGroup,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

// Custom hook for using the chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
