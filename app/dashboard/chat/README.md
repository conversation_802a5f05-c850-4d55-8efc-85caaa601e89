# Dashboard Chat

This is the chat system for the dashboard interface. It provides real-time messaging capabilities using WebSocket connections.

## Features

- **Real-time messaging** via WebSocket connections
- **Chat rooms** with group and direct messaging support
- **Message search** functionality
- **Responsive design** with dark/light theme support
- **Emoji picker** for enhanced messaging
- **Connection status** indicators
- **Message history** with date grouping

## Architecture

### Folder Structure

```
app/dashboard/chat/
├── _logics/
│   ├── chat_context.tsx         # React context for chat state management
│   ├── websocket_service.ts     # WebSocket service for real-time communication
│   ├── chat_utils.ts           # Utility functions for common chat operations
│   └── types.ts                # TypeScript interfaces and types
├── widgets/
│   ├── ChatContainer.tsx       # Main chat container
│   ├── ChatSidebar.tsx         # Chat sidebar with room list
│   ├── ChatMessageArea.tsx     # Message display area
│   └── ChatMessageInput.tsx    # Message input component
├── page.tsx                    # Main chat page
└── README.md                   # This file
```

### Key Components

1. **ChatContainer**: Main wrapper component that provides the chat context
2. **ChatSidebar**: Left sidebar showing available chat rooms and search
3. **ChatMessageArea**: Central area displaying messages with date grouping
4. **ChatMessageInput**: Bottom input area for typing and sending messages

### WebSocket Integration

The chat system uses WebSocket connections for real-time communication:

- **Group Chat**: `{NEXT_PUBLIC_WS_CHAT_BASE_URL}/{room_id}?user_id={user_id}`
- **Direct Chat**: `{NEXT_PUBLIC_WS_CHAT_BASE_URL}/direct/{recipient_id}?user_id={user_id}`

### Environment Variables

Make sure to set the following environment variable:

```env
NEXT_PUBLIC_WS_CHAT_BASE_URL=ws://your-websocket-server.com/ws/chat
```

## Usage

1. Navigate to `/dashboard/chat` in the dashboard
2. Select a chat room from the sidebar
3. Start messaging in real-time
4. Use the search functionality to find specific chats or messages
5. Toggle between light and dark themes

## Demo Data

The dashboard chat includes demo data for testing:
- General Discussion room
- Project Updates room

## Dependencies

- React Context for state management
- WebSocket for real-time communication
- date-fns for date formatting
- emoji-picker-react for emoji support
- Framer Motion for animations (inherited from dashboard)

## Notes

- This is a UI-only implementation without API integration
- WebSocket connections are handled by the websocket_service
- The chat context manages all state without external API calls
- Demo data is automatically loaded for testing purposes
