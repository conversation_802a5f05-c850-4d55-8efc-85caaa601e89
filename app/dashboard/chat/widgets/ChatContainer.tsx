'use client';

import React from 'react';
import { ChatSidebar } from './ChatSidebar';
import { ChatMessageArea } from './ChatMessageArea';
import { ChatMessageInput } from './ChatMessageInput';
import { ChatProvider } from '../_logics/chat_context';

export const ChatContainer = () => {
  return (
    <ChatProvider>
      <div className="flex flex-col h-full bg-white">
        {/* Chat main container */}
        <div className="flex flex-1 overflow-hidden">
          <ChatSidebar />
          <div className="flex-1 flex flex-col">
            <ChatMessageArea />
            <ChatMessageInput />
          </div>
        </div>
      </div>
    </ChatProvider>
  );
};
