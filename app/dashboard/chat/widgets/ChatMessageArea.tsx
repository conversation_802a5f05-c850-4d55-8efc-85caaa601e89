'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useChat } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

export const ChatMessageArea = () => {
  const { state, searchMessages } = useChat();
  const { isDark } = useTeacherTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const activeRoom = state.rooms.find(room => room.id === state.activeRoomId);
  const messages = state.activeRoomId ? state.messages[state.activeRoomId] || [] : [];

  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (state.activeRoomId) {
      searchMessages(state.activeRoomId, query);
    }
  };

  const formatMessageTime = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(timestamp);
  };

  const formatMessageDate = (timestamp: Date) => {
    const today = new Date();
    const messageDate = new Date(timestamp);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    }
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }
    
    return messageDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: messageDate.getFullYear() !== today.getFullYear() ? 'numeric' : undefined,
    });
  };

  // Group messages by date
  const groupedMessages = messages.reduce((groups: any, message) => {
    const dateKey = formatMessageDate(message.timestamp);
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(message);
    return groups;
  }, {});

  if (!state.activeRoomId) {
    return (
      <div className={`flex-1 flex items-center justify-center ${isDark ? 'bg-[#252B42]' : 'bg-gray-50'}`}>
        <div className="text-center">
          <svg className={`w-16 h-16 ${isDark ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <h3 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'} mb-2`}>
            Welcome to Dashboard Chat
          </h3>
          <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Select a chat room to start messaging
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex-1 flex flex-col ${isDark ? 'bg-[#252B42]' : 'bg-white'}`}>
      {/* Chat Header */}
      <div className={`px-4 py-3 border-b ${isDark ? 'border-[#384058] bg-[#252B42]' : 'border-gray-200 bg-white'} flex items-center justify-between`}>
        <div className="flex items-center">
          <img
            src="/logo.png"
            alt={activeRoom?.name}
            className="w-8 h-8 rounded-full object-cover shadow-sm border border-gray-200"
          />
          <div className="ml-3">
            <h2 className={`font-medium text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>
              {activeRoom?.name || 'Unknown Room'}
            </h2>
            <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              {activeRoom?.participants?.length || 0} members
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Search Toggle */}
          <button
            onClick={() => setIsSearchActive(!isSearchActive)}
            className={`p-2 rounded-full transition-colors ${isDark ? 'hover:bg-[#384058] text-gray-300' : 'hover:bg-gray-100 text-gray-600'} ${isSearchActive ? (isDark ? 'bg-[#384058]' : 'bg-gray-100') : ''}`}
            title="Search messages"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>

          {/* More Options */}
          <div className="relative">
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className={`p-2 rounded-full transition-colors ${isDark ? 'hover:bg-[#384058] text-gray-300' : 'hover:bg-gray-100 text-gray-600'}`}
              title="More options"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>

            {dropdownOpen && (
              <div className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg ${isDark ? 'bg-[#1e232e] border border-[#384058]' : 'bg-white border border-gray-200'} z-10`}>
                <div className="py-1">
                  <button className={`block w-full text-left px-4 py-2 text-sm ${isDark ? 'text-gray-300 hover:bg-[#384058]' : 'text-gray-700 hover:bg-gray-100'}`}>
                    Room Info
                  </button>
                  <button className={`block w-full text-left px-4 py-2 text-sm ${isDark ? 'text-gray-300 hover:bg-[#384058]' : 'text-gray-700 hover:bg-gray-100'}`}>
                    Clear Messages
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Search Bar */}
      {isSearchActive && (
        <div className={`px-4 py-2 border-b ${isDark ? 'border-[#384058] bg-[#1e232e]' : 'border-gray-200 bg-gray-50'}`}>
          <div className="relative">
            <input
              type="text"
              placeholder="Search messages..."
              value={searchQuery}
              onChange={handleSearch}
              className={`w-full px-3 py-2 pl-8 text-sm rounded-lg border ${isDark ? 'bg-[#252B42] border-[#384058] text-white placeholder-gray-400' : 'bg-white border-gray-200 text-gray-900 placeholder-gray-500'} focus:outline-none focus:ring-2 focus:ring-[#006060] focus:border-transparent`}
              autoFocus
            />
            <svg
              className={`absolute left-2.5 top-2.5 w-4 h-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {Object.keys(groupedMessages).length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <svg className={`w-12 h-12 ${isDark ? 'text-gray-600' : 'text-gray-400'} mb-3`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} text-center`}>
              No messages yet. Start the conversation!
            </p>
          </div>
        ) : (
          Object.entries(groupedMessages).map(([date, dateMessages]: [string, any]) => (
            <div key={date}>
              {/* Date Separator */}
              <div className="flex items-center justify-center my-4">
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${isDark ? 'bg-[#384058] text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                  {date}
                </div>
              </div>

              {/* Messages for this date */}
              {dateMessages.map((message: any, index: number) => (
                <div key={message.id} className="flex items-start space-x-3 mb-4">
                  <img
                    src={message.senderAvatar || '/logo.png'}
                    alt={message.senderName}
                    className="w-8 h-8 rounded-full object-cover shadow-sm border border-gray-200"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`font-medium text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>
                        {message.senderName}
                      </span>
                      <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                        {formatMessageTime(message.timestamp)}
                      </span>
                    </div>
                    <div className={`text-sm ${isDark ? 'text-gray-200' : 'text-gray-700'} break-words`}>
                      {message.content}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Click outside to close dropdown */}
      {dropdownOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setDropdownOpen(false)}
        />
      )}
    </div>
  );
};
