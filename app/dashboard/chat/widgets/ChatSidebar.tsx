'use client';

import React, { useState } from 'react';
import { useChat } from '../_logics/chat_context';
import { format } from 'date-fns';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';

export const ChatSidebar = () => {
  const { state, setActiveRoom, searchChats } = useChat();
  const [searchTerm, setSearchTerm] = useState('');
  const { isDark } = useTeacherTheme();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const [activeTab, setActiveTab] = useState<'all' | 'direct' | 'group'>('all');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchTerm(query);
    searchChats(query);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) {
      return "now";
    } else if (minutes < 60) {
      return `${minutes}m`;
    } else if (hours < 24) {
      return `${hours}h`;
    } else if (days < 7) {
      return `${days}d`;
    } else {
      return format(timestamp, 'MMM d');
    }
  };

  const filteredRooms = state.rooms.filter(room => {
    if (activeTab === 'all') return true;
    if (activeTab === 'group') return room.isGroup;
    if (activeTab === 'direct') return !room.isGroup;
    return true;
  });

  return (
    <div className={`w-[280px] flex-shrink-0 border-r ${isDark ? 'border-[#384058] bg-[#252B42]' : 'border-gray-200 bg-white'} h-full flex flex-col`}>
      <div className={`px-3 py-3 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <img src="/logo.png" alt="LearnKonnect" className="w-6 h-6 mr-2" />
            <div className="flex flex-col">
              <div className="flex items-center">
                <h2 className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base`}>LearnKonnect</h2>
                <span className={`font-medium ${isDark ? 'text-white' : 'text-[#006060]'} text-base ml-1`}>chat</span>
              </div>
              <p className={`text-xs ${isDark ? 'text-gray-200' : 'text-gray-500'}`}>Dashboard chat</p>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={`p-1.5 rounded-full transition-colors ${isDark ? 'hover:bg-[#384058] text-gray-300' : 'hover:bg-gray-100 text-gray-600'} ${isRefreshing ? 'animate-spin' : ''}`}
            title="Refresh chat rooms"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* Search */}
      <div className={`px-3 py-2 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="relative">
          <input
            type="text"
            placeholder="Search chats..."
            value={searchTerm}
            onChange={handleSearch}
            className={`w-full px-3 py-2 pl-8 text-sm rounded-lg border ${isDark ? 'bg-[#1e232e] border-[#384058] text-white placeholder-gray-400' : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500'} focus:outline-none focus:ring-2 focus:ring-[#006060] focus:border-transparent`}
          />
          <svg
            className={`absolute left-2.5 top-2.5 w-4 h-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* Tabs */}
      <div className={`px-3 py-2 border-b ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex space-x-1">
          {[
            { key: 'all', label: 'All' },
            { key: 'group', label: 'Groups' },
            { key: 'direct', label: 'Direct' },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-3 py-1.5 text-xs font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? isDark
                    ? 'bg-[#006060] text-white'
                    : 'bg-[#006060] text-white'
                  : isDark
                  ? 'text-gray-300 hover:bg-[#384058]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chat Rooms List */}
      <div className="flex-1 overflow-y-auto">
        {state.isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#006060]"></div>
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 px-4">
            <svg className={`w-12 h-12 ${isDark ? 'text-gray-600' : 'text-gray-400'} mb-3`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'} text-center`}>
              {searchTerm ? 'No chats found' : 'No chat rooms available'}
            </p>
            {searchTerm && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  searchChats('');
                }}
                className={`mt-2 text-xs ${isDark ? 'text-[#006060]' : 'text-[#006060]'} hover:underline`}
              >
                Clear search
              </button>
            )}
          </div>
        ) : (
          filteredRooms
            .slice(0, 20) // Limit to 20 rooms for performance
            .map((room) => (
            <div
              key={room.id}
              className={`flex items-center p-3 cursor-pointer transition-colors duration-200 ${isDark ?
                (state.activeRoomId === room.id ? 'bg-[#1e232e]' : 'hover:bg-[#1e232e]') :
                (state.activeRoomId === room.id ? 'bg-gray-100' : 'hover:bg-gray-50')
              } rounded-lg mx-1 my-0.5`}
              onClick={() => setActiveRoom(room.id)}
            >
              <div className="relative">
                {/* Always use logo for chat avatars */}
                <img
                  src="/logo.png"
                  alt={room.name}
                  className={`w-10 h-10 rounded-full object-cover shadow-md border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}
                />
              </div>
              <div className="ml-3 flex-1 overflow-hidden">
                <div className="flex justify-between items-center">
                  <h3 className={`font-medium text-sm ${isDark ? 'text-white' : 'text-gray-900'} truncate`}>
                    {room.name}
                  </h3>
                  <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} ml-2 flex-shrink-0`}>
                    {formatTimestamp(room.timestamp)}
                  </span>
                </div>
                <p className={`text-xs ${isDark ? 'text-gray-300' : 'text-gray-600'} truncate mt-0.5`}>
                  {room.lastMessage || 'No messages yet'}
                </p>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Connection Status */}
      <div className={`px-3 py-2 border-t ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${state.error ? 'bg-red-500' : 'bg-green-500'}`}></div>
            <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              {state.error ? 'Disconnected' : 'Connected'}
            </span>
          </div>
          <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            {filteredRooms.length} rooms
          </span>
        </div>
      </div>
    </div>
  );
};
