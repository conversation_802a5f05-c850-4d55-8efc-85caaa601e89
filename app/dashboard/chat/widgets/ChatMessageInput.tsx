'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useChat } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import dynamic from 'next/dynamic';

// Importing type for TS checking, but using dynamic import for the component
import type { Theme } from 'emoji-picker-react';

// Dynamically import EmojiPicker to avoid SSR issues
const EmojiPicker = dynamic(() => import('emoji-picker-react'), { ssr: false });

export const ChatMessageInput = () => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const { sendMessage, state } = useChat();
  const { isDark } = useTeacherTheme();

  // Handle emoji selection
  const handleEmojiClick = (emojiData: any) => {
    // Different versions of emoji-picker-react have different response formats
    // This approach handles multiple versions
    const emoji = emojiData.emoji || (emojiData.srcElement && emojiData.srcElement.innerText) || '';
    setMessage(prevMessage => prevMessage + emoji);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && state.activeRoomId) {
      sendMessage(message.trim());
      setMessage('');
      setShowEmojiPicker(false);
    }
  };

  // Handle click outside emoji picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target as Node) &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  if (!state.activeRoomId) {
    return null;
  }

  return (
    <div className={`${isDark ? 'bg-[#252B42] border-[#384058]' : 'bg-white border-gray-200'} border-t py-2.5 px-4`}>
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <div className="relative">
          <button
            ref={emojiButtonRef}
            type="button"
            className={`text-gray-400 hover:text-gray-600 p-2 rounded-full transition-colors ${isDark ? 'hover:bg-[#384058]' : 'hover:bg-gray-100'}`}
            title="Emoji"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>

          {/* Emoji Picker */}
          {showEmojiPicker && (
            <div ref={emojiPickerRef} className="absolute bottom-12 left-0 z-50">
              <EmojiPicker
                onEmojiClick={handleEmojiClick}
                theme={isDark ? ('dark' as Theme) : ('light' as Theme)}
                width={300}
                height={400}
              />
            </div>
          )}
        </div>

        <div className="flex-1 relative">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            rows={1}
            className={`w-full px-3 py-2 pr-12 text-sm rounded-lg border resize-none ${
              isDark 
                ? 'bg-[#1e232e] border-[#384058] text-white placeholder-gray-400 focus:border-[#006060]' 
                : 'bg-gray-50 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-[#006060]'
            } focus:outline-none focus:ring-2 focus:ring-[#006060] focus:ring-opacity-50 transition-colors`}
            style={{
              minHeight: '40px',
              maxHeight: '120px',
            }}
            onInput={(e) => {
              const target = e.target as HTMLTextAreaElement;
              target.style.height = 'auto';
              target.style.height = Math.min(target.scrollHeight, 120) + 'px';
            }}
          />

          {/* Attachment Button */}
          <button
            type="button"
            className={`absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1 rounded transition-colors ${isDark ? 'hover:bg-[#384058]' : 'hover:bg-gray-100'}`}
            title="Attach file"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
            </svg>
          </button>
        </div>

        <button
          type="submit"
          disabled={!message.trim()}
          className={`p-2 rounded-full transition-colors ${
            message.trim()
              ? 'bg-[#006060] text-white hover:bg-[#005050]'
              : isDark
              ? 'bg-[#384058] text-gray-500 cursor-not-allowed'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }`}
          title="Send message"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>

      {/* Connection Status */}
      {state.error && (
        <div className="mt-2 flex items-center text-xs text-red-500">
          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Connection lost. Messages may not be delivered.
        </div>
      )}
    </div>
  );
};
